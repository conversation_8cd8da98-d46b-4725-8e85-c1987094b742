"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useDropdown = useDropdown;
var React = _interopRequireWildcard(require("react"));
var _useControllableReducer = require("../utils/useControllableReducer");
var _useDropdown = require("./useDropdown.types");
var _dropdownReducer = require("./dropdownReducer");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 *
 * Demos:
 *
 * - [Menu](https://mui.com/base-ui/react-menu/#hooks)
 *
 * API:
 *
 * - [useDropdown API](https://mui.com/base-ui/react-menu/hooks-api/#use-dropdown)
 */
function useDropdown(parameters = {}) {
  const {
    defaultOpen,
    onOpenChange,
    open: openProp,
    componentName = 'useDropdown'
  } = parameters;
  const [popupId, setPopupId] = React.useState('');
  const [triggerElement, setTriggerElement] = React.useState(null);
  const lastActionType = React.useRef(null);
  const handleStateChange = React.useCallback((event, field, value, reason) => {
    if (field === 'open') {
      onOpenChange == null || onOpenChange(event, value);
    }
    lastActionType.current = reason;
  }, [onOpenChange]);
  const controlledProps = React.useMemo(() => openProp !== undefined ? {
    open: openProp
  } : {}, [openProp]);
  const [state, dispatch] = (0, _useControllableReducer.useControllableReducer)({
    controlledProps,
    initialState: defaultOpen ? {
      open: true,
      changeReason: null
    } : {
      open: false,
      changeReason: null
    },
    onStateChange: handleStateChange,
    reducer: _dropdownReducer.dropdownReducer,
    componentName
  });
  React.useEffect(() => {
    if (!state.open && lastActionType.current !== null && lastActionType.current !== _useDropdown.DropdownActionTypes.blur) {
      triggerElement == null || triggerElement.focus();
    }
  }, [state.open, triggerElement]);
  const contextValue = {
    state,
    dispatch,
    popupId,
    registerPopup: setPopupId,
    registerTrigger: setTriggerElement,
    triggerElement
  };
  return {
    contextValue,
    open: state.open
  };
}